import axios from '../utils/axios';
// import { API_URL } from '../config';

// 用于存储正在进行的会话更新
const pendingUpdates = new Map<string, any>();

export const createSession = async (question: string, fingerprint?: string, options?: { userData?: any, spreadId?: string }): Promise<{ sessionId: string }> => {
  try {
    const token = localStorage.getItem('token');

    // 构建请求体
    const requestBody: any = { question };

    // 如果没有token但有指纹，说明是匿名用户
    if (!token && fingerprint) {
      requestBody.fingerprint = fingerprint;
    } else if (!token) {
      throw new Error('No authentication token found');
    }

    // 添加可选参数
    if (options) {
      if (options.userData) {
        requestBody.userData = options.userData;
      }
      if (options.spreadId) {
        requestBody.spreadId = options.spreadId;
      }
    }

    const response = await axios.post('/api/session', requestBody);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to create session');
    }

    if (response.data.session && response.data.session.id) {
      const sessionId = response.data.session.id;
      localStorage.setItem('sessionId', sessionId);
      return { sessionId };
    } else {
      throw new Error('Invalid session data received');
    }
  } catch (error) {
    // console.error('Error creating session:', error);
    throw error;
  }
};

export const getActiveSession = async () => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await axios.get('/api/session/active');

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch active session');
    }

    return response.data.session;
  } catch (error) {
    // console.error('Error fetching active session:', error);
    throw error;
  }
};

// 更新会话的本地缓存
const updateLocalSessionCache = (sessionId: string, updateData: any) => {
  if (!sessionId || sessionId === 'undefined') {
    // console.error('Invalid sessionId in updateLocalSessionCache');
    return;
  }
  const currentData = pendingUpdates.get(sessionId) || {};
  pendingUpdates.set(sessionId, { ...currentData, ...updateData });
};

// 获取会话的本地缓存
export const getLocalSessionCache = (sessionId: string) => {
  if (!sessionId || sessionId === 'undefined') {
    // console.error('Invalid sessionId in getLocalSessionCache');
    return null;
  }
  return pendingUpdates.get(sessionId);
};

// 异步更新会话
export const updateSession = async (sessionId: string, updateData: any, waitForComplete: boolean = false) => {
  try {
    if (!sessionId || sessionId === 'undefined') {
      throw new Error('Invalid or missing sessionId');
    }

    // 立即更新本地缓存
    updateLocalSessionCache(sessionId, updateData);

    // 如果不需要等待完成，就直接返回
    if (!waitForComplete) {
      // 异步执行更新
      sendUpdateToServer(sessionId, updateData).catch(() => {
        // console.error('Background session update failed:', error);
      });
      return;
    }

    // 需要等待完成的情况
    await sendUpdateToServer(sessionId, updateData);
  } catch (error) {
    // console.error('Error updating session:', error);
    throw error;
  }
};

// 发送更新到服务器
const sendUpdateToServer = async (sessionId: string, updateData: any) => {
  if (!sessionId || sessionId === 'undefined') {
    throw new Error('Invalid sessionId in sendUpdateToServer');
  }

  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('No authentication token found');
  }

  try {
    const response = await axios.patch(`/api/session/${sessionId}`, updateData);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update session');
    }
    
    return response.data.session;
  } catch (error) {
    throw error;
  }
};

export const getSessionAdvice = async (sessionId: string): Promise<string[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await axios.get(`/api/session/${sessionId}/advice`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch session advice');
    }

    return response.data.advice || [];
  } catch (error) {
    // console.error('Error fetching session advice:', error);
    throw error;
  }
};

