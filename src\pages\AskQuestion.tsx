import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import LoginPrompt from '../components/LoginPrompt';
import { createSession } from '../services/sessionService';
import { getCurrentUser } from '../services/userService';
import { checkAnonymousEligibility } from '../services/anonymousService';
import { getBrowserFingerprint } from '../utils/fingerprint';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import VipPromptDialog from '../components/VipPromptDialog';
import RemainingReadsDisplay from '../components/RemainingReadsDisplay';
import SEO from '../components/SEO';

import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

const AskQuestion: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { user, setUser } = useUser();
  const { t, i18n } = useTranslation();
  const [question, setQuestion] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('love');
  const [selectedSubcategory, setSelectedSubcategory] = useState('current_relationship');

  const validateQuestion = (question: string): { isValid: boolean; errorMessage: string } => {
    const trimmedQuestion = question.trim();
    const currentLang = i18n.language;
    
    // 检查问题长度
    if (trimmedQuestion.length < 5) {
      return { isValid: false, errorMessage: t('home.validation.too_short') };
    }
    if (trimmedQuestion.length > 200) {
      return { isValid: false, errorMessage: t('home.validation.too_long') };
    }

    // 检查是否包含表情符号
    const emojiRegex = /[\u{1F000}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;
    if (emojiRegex.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.contains_emoji') };
    }

    // 根据语言设置不同的验证规则
    if (currentLang === 'zh-CN' || currentLang === 'zh-TW') {
      // 中文验证规则
      if (!/[\u4e00-\u9fa5]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else if (currentLang === 'ja') {
      // 日文验证规则
      if (!/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else {
      // 英文验证规则
      if (!/[a-zA-Z]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    }

    // 检查是否为纯数字
    if (/^\d+$/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查重复字符
    if (/(.)\1{4,}/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查测试文本 - 根据语言设置不同的测试词
    const testPatterns = {
      'zh-CN': ['测试', 'test', '123', 'abc'],
      'zh-TW': ['測試', 'test', '123', 'abc'],
      'ja': ['テスト', 'test', '123', 'abc'],
      'en': ['test', '123', 'abc']
    };

    if (testPatterns[currentLang as keyof typeof testPatterns].some(pattern => 
      trimmedQuestion.toLowerCase().includes(pattern.toLowerCase())
    )) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    return { isValid: true, errorMessage: '' };
  };

  // 问题灵感数据 - 使用翻译
  const getQuestionInspirations = () => {
    const categories = t('ask_question.inspiration.categories', { returnObjects: true }) as any;

    return [
      {
        id: 'love',
              icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-red-500">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" fill="currentColor"/>
        </svg>
      ),
        title: categories.love?.title || '爱情类',
        subcategories: [
          {
            id: 'current_relationship',
            title: categories.love?.subcategories?.current_relationship?.title || '现有关系',
            questions: categories.love?.subcategories?.current_relationship?.questions || []
          },
          {
            id: 'future_love',
            title: categories.love?.subcategories?.future_love?.title || '未来发展',
            questions: categories.love?.subcategories?.future_love?.questions || []
          },
          {
            id: 'single_life',
            title: categories.love?.subcategories?.single_life?.title || '单身桃花',
            questions: categories.love?.subcategories?.single_life?.questions || []
          }
        ]
      },
      {
        id: 'career',
        icon: '👔',
        title: categories.career?.title || '事业类',
        subcategories: [
          {
            id: 'job_hunting',
            title: categories.career?.subcategories?.job_hunting?.title || '求职面试',
            questions: categories.career?.subcategories?.job_hunting?.questions || []
          },
          {
            id: 'career_development',
            title: categories.career?.subcategories?.career_development?.title || '职业发展',
            questions: categories.career?.subcategories?.career_development?.questions || []
          },
          {
            id: 'entrepreneurship',
            title: categories.career?.subcategories?.entrepreneurship?.title || '创业投资',
            questions: categories.career?.subcategories?.entrepreneurship?.questions || []
          }
        ]
      },
      {
        id: 'wealth',
        icon: '💰',
        title: categories.wealth?.title || '财富类',
        subcategories: [
          {
            id: 'investment',
            title: categories.wealth?.subcategories?.investment?.title || '投资理财',
            questions: categories.wealth?.subcategories?.investment?.questions || []
          },
          {
            id: 'financial_status',
            title: categories.wealth?.subcategories?.financial_status?.title || '财务状况',
            questions: categories.wealth?.subcategories?.financial_status?.questions || []
          }
        ]
      },
      {
        id: 'interpersonal',
        icon: '👥',
        title: categories.interpersonal?.title || '人际关系类',
        subcategories: [
          {
            id: 'friendship',
            title: categories.interpersonal?.subcategories?.friendship?.title || '友情关系',
            questions: categories.interpersonal?.subcategories?.friendship?.questions || []
          },
          {
            id: 'workplace',
            title: categories.interpersonal?.subcategories?.workplace?.title || '职场关系',
            questions: categories.interpersonal?.subcategories?.workplace?.questions || []
          },
          {
            id: 'family',
            title: categories.interpersonal?.subcategories?.family?.title || '家庭关系',
            questions: categories.interpersonal?.subcategories?.family?.questions || []
          }
        ]
      },
      {
        id: 'growth',
        icon: '⭐',
        title: categories.growth?.title || '个人成长类',
        subcategories: [
          {
            id: 'life_direction',
            title: categories.growth?.subcategories?.life_direction?.title || '人生方向',
            questions: categories.growth?.subcategories?.life_direction?.questions || []
          },
          {
            id: 'personal_growth',
            title: categories.growth?.subcategories?.personal_growth?.title || '个人成长',
            questions: categories.growth?.subcategories?.personal_growth?.questions || []
          }
        ]
      },
      {
        id: 'past_life',
        icon: '♾️',
        title: categories.past_life?.title || '前世今生类',
        subcategories: [
          {
            id: 'past_life_connection',
            title: categories.past_life?.subcategories?.past_life_connection?.title || '前世缘分',
            questions: categories.past_life?.subcategories?.past_life_connection?.questions || []
          },
          {
            id: 'soul_mission',
            title: categories.past_life?.subcategories?.soul_mission?.title || '灵魂使命',
            questions: categories.past_life?.subcategories?.soul_mission?.questions || []
          }
        ]
      }
    ];
  };

  const questionInspirations = getQuestionInspirations();

  const handleQuestionClick = (question: string) => {
    setQuestion(question);
    setErrorMessage('');
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    // 当切换大类别时，自动选择第一个小类别
    const category = questionInspirations.find(cat => cat.id === categoryId);
    if (category && category.subcategories.length > 0) {
      setSelectedSubcategory(category.subcategories[0].id);
    }
  };

  // 添加自动刷新用户信息的 effect
  useEffect(() => {
    const refreshUserData = async () => {
      if (user) {  // 只在用户已登录时刷新
        try {
          const userData = await getCurrentUser();
          setUser(userData);
        } catch (error) {
          // 用户数据刷新失败
        }
      }
    };

    refreshUserData();
  }, []); // 组件加载时执行一次

  // 清理本地存储的 effect
  useEffect(() => {
    // 清除所有与会话相关的本地存储
    localStorage.removeItem('sessionId');
    localStorage.removeItem('userQuestion');
    localStorage.removeItem('selectedSpread');
    localStorage.removeItem('selectedCardsInfo');
    localStorage.removeItem('tarotDialogState');
  }, []);

  // 检查用户权限
  const checkUserPermission = async () => {
    if (!user) {
      // 匿名用户需要检查是否已使用过免费机会
      try {
        const fingerprint = await getBrowserFingerprint();
        const eligibility = await checkAnonymousEligibility(fingerprint);
        if (!eligibility.canUse) {
          setShowLoginPrompt(true);
          return false;
        }
        return true;
      } catch (error) {
        console.error('检查匿名用户权限失败:', error);
        setShowLoginPrompt(true);
        return false;
      }
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };

  const handleStartReading = async () => {
    const hasPermission = await checkUserPermission();
    if (!hasPermission) return;

    if (!question.trim()) {
      // 如果问题为空，不执行任何操作
      return;
    }

    try {
      // 为匿名用户添加指纹信息
      const sessionData: any = { question: question.trim() };
      if (!user) {
        const fingerprint = await getBrowserFingerprint();
        sessionData.fingerprint = fingerprint;
      }

      // 创建新的会话
      const { sessionId } = await createSession(sessionData.question, sessionData.fingerprint);

      // 保存会话信息到本地存储
      localStorage.setItem('sessionId', sessionId);
      localStorage.setItem('userQuestion', question.trim());

      // 导航到牌阵选择页面
      navigate('/reading/spread');
    } catch (error) {
      // 创建会话失败处理
      console.error('创建会话失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 移除图片加载检查，允许用户继续操作
    // 即使图片未加载完成，也允许用户提交
    
    const validation = validateQuestion(question);
    if (!validation.isValid) {
      setErrorMessage(validation.errorMessage);
      return;
    }

    handleStartReading();
  };

  return (
    <div className="main-container min-h-screen flex flex-col relative">
      <SEO
        title={t('ask_question.meta.title')}
        description={t('ask_question.meta.description')}
        keywords={t('ask_question.meta.keywords')}
      />
      <LandingBackground />

      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mt-8 sm:mt-10">
            <h1 className="main-title mb-2 sm:mb-3">{t('ask_question.title')}</h1>
            <p className="sub-title mb-4 sm:mb-6">{t('ask_question.subtitle')}</p>
          </div>

          <form onSubmit={handleSubmit} className="mt-8 sm:mt-12">
            <div className="max-w-full sm:max-w-[85%] lg:max-w-4xl mx-auto">
              <div className="relative" style={{ position: 'relative', zIndex: 1 }}>
                  <textarea
                    value={question}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      if (newValue.length <= 200) {
                        setQuestion(newValue);
                        setErrorMessage('');
                      }
                    }}
                    maxLength={200}
                    placeholder={t('home.placeholder')}
                    className="body-text block w-full px-4 sm:px-5 py-3 sm:py-4 pr-14 rounded-xl dark:bg-black/40 bg-[#F4F4F5] backdrop-blur-xl
                             dark:placeholder-gray-400 placeholder-gray-500 h-36 sm:h-40
                             focus:outline-none
                             transition-all duration-300 ease-in-out
                             border border-purple-500/20
                             focus:border-purple-500/40 focus:ring-0 focus:outline-0
                             hover:bg-[#E8E8EA] dark:hover:bg-black/60
                             hover:border-purple-500/40 dark:hover:border-purple-400/50
                             hover:shadow-lg dark:hover:shadow-purple-500/10
                             mb-4 sm:mb-5 resize-none"
                    style={{ fontSize: '16px', position: 'relative', zIndex: 1 }}
                  />

                  {/* 发送按钮 - 圆形向上箭头 */}
                  <button
                    type="submit"
                    disabled={!question.trim()}
                    className={`absolute bottom-[0.75rem] sm:bottom-[1rem] right-3 w-9 h-9 sm:w-8 sm:h-8 rounded-full flex items-center justify-center
                             transition-all duration-200 ease-in-out
                             ${question.trim()
                               ? 'bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white shadow-lg hover:shadow-xl'
                               : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                             }`}
                    style={{
                      position: 'absolute',
                      zIndex: 100,
                      // iOS Safari 特殊优化
                      WebkitTransform: 'translateZ(0)',
                      transform: 'translateZ(0)',
                      WebkitBackfaceVisibility: 'hidden',
                      backfaceVisibility: 'hidden',
                      // 确保触摸区域正确
                      minHeight: '36px',
                      minWidth: '36px',
                      touchAction: 'manipulation'
                    }}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="transform -rotate-90"
                      style={{ pointerEvents: 'none' }}
                    >
                      <path d="M5 12h14M12 5l7 7-7 7"/>
                    </svg>
                  </button>

                  {/* 字数统计 - 移动到左下角 */}
                  <div className="counter-text absolute bottom-[0.75rem] sm:bottom-[1rem] left-3.5 sm:left-4 text-xs sm:text-sm pointer-events-none select-none font-semibold tracking-wide tabular-nums px-1.5 py-0.5 rounded"
                       style={{ zIndex: 50 }}>
                    <span className="hidden sm:inline">
                      <span className="font-numeric">{question.length}</span>
                      <span>/</span>
                      <span className="font-numeric">200</span>
                    </span>
                    {user && (
                      <span className="sm:hidden flex items-center">
                        {user.vipStatus === 'active' ? (
                          <span>{t('common.remaining_reads_vip')}</span>
                        ) : (
                          <span>{t('common.remaining_reads_count')}{user.remainingReads}</span>
                        )}
                      </span>
                    )}
                  </div>
                </div>

                {errorMessage && (
                  <div className="mt-3 p-3 rounded-lg bg-red-500/10 border border-red-500/20">
                    <p className="body-text text-red-400">{errorMessage}</p>
                  </div>
                )}
              </div>
          </form>

          {/* 问题灵感组件 */}
          <div className="max-w-full sm:max-w-[85%] lg:max-w-4xl mx-auto mt-8">
            <div className="relative backdrop-blur-xl p-6 rounded-2xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20">
              <div className="flex items-center gap-3 mb-8">
                <span className="text-2xl">💡</span>
                <h2 className="text-lg font-medium dark:text-white text-gray-900">{t('ask_question.inspiration.title')}</h2>
              </div>

              {/* 大分类标签 */}
              <div className="flex flex-wrap gap-2 mb-6">
                {questionInspirations.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => handleCategoryChange(category.id)}
                    className={`flex items-center gap-2 px-3 py-1.5 rounded-lg
                             transition-all duration-200 text-sm font-medium
                             ${selectedCategory === category.id
                               ? 'dark:bg-purple-500/20 bg-purple-500/15 dark:text-purple-200 text-purple-700 shadow-sm'
                               : 'dark:text-gray-400 text-gray-600 hover:dark:text-purple-300 hover:text-purple-600 hover:dark:bg-purple-500/10 hover:bg-purple-500/5'
                             }`}
                  >
                    <span className="text-sm">{category.icon}</span>
                    <span>{category.title}</span>
                  </button>
                ))}
              </div>

              {/* 小分类标签 */}
              {(() => {
                const currentCategory = questionInspirations.find(cat => cat.id === selectedCategory);
                if (!currentCategory) return null;

                return (
                  <div className="flex flex-wrap gap-2 mb-6">
                    {currentCategory.subcategories.map((subcategory) => (
                      <button
                        key={subcategory.id}
                        onClick={() => setSelectedSubcategory(subcategory.id)}
                        className={`px-3 py-1 rounded-md text-xs font-medium transition-all duration-200
                                 ${selectedSubcategory === subcategory.id
                                   ? 'dark:bg-purple-500/20 bg-purple-500/15 dark:text-purple-200 text-purple-700 shadow-sm'
                                   : 'dark:text-gray-500 text-gray-500 hover:dark:text-purple-300 hover:text-purple-600 hover:dark:bg-purple-500/10 hover:bg-purple-500/5'
                                 }`}
                      >
                        {subcategory.title}
                      </button>
                    ))}
                  </div>
                );
              })()}

              {/* 当前选中小分类的问题列表 */}
              {(() => {
                const currentCategory = questionInspirations.find(cat => cat.id === selectedCategory);
                if (!currentCategory) return null;

                const currentSubcategory = currentCategory.subcategories.find(sub => sub.id === selectedSubcategory);
                if (!currentSubcategory) return null;

                return (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-2 sm:gap-3">
                      {currentSubcategory.questions.map((q: string, index: number) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleQuestionClick(q)}
                          className="flex items-center gap-3 sm:gap-4 p-3 sm:p-4 rounded-xl
                                   dark:bg-black/10 bg-white/60
                                   border-0 hover:dark:bg-black/20 hover:bg-white/80
                                   hover:shadow-md dark:hover:shadow-purple-500/5
                                   transition-all duration-200 text-left group"
                        >
                          <span className="question-icon text-lg flex-shrink-0">✦</span>
                          <span className="body-text dark:text-gray-200 text-gray-700 flex-1 leading-relaxed">
                            {q}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      </div>
      <div className="relative z-10">
        <Footer />
      </div>

      {/* VIP提示弹窗 */}
      <VipPromptDialog 
        isOpen={showVipPrompt} 
        onCancel={() => setShowVipPrompt(false)}
      />
      
      {/* 登录提示弹窗 */}
      <LoginPrompt 
        isOpen={showLoginPrompt} 
        onClose={() => setShowLoginPrompt(false)}
      />
    </div>
  );
};

export default AskQuestion;
