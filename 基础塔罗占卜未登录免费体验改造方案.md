# 基础塔罗占卜未登录免费体验改造方案

## 一、需求分析

### 当前状态
- 新用户注册后赠送3次免费占卜次数（非重复指纹下）
- 只有登录状态下才可以使用基础塔罗占卜
- 登录后需要判断是否有足够的次数或者是否为VIP

### 目标状态
- 未登录状态下可以使用基础塔罗占卜，每个人只有1次免费机会
- 使用浏览器指纹检查来判断是否已使用过免费机会
- 用户进行一次免费占卜后，剩余次数变为0
- 此时提示"登录后获得更多占卜次数"，并提供导航至登录页面的按钮
- 用户注册登录后额外赠送2次免费占卜（总共3次，减去未登录时使用的1次）

## 二、技术方案分析

### 2.1 数据库设计方案

#### 方案：新增独立表（推荐）
创建新表 `anonymous_divination_records` 来记录未登录占卜：

```sql
CREATE TABLE `anonymous_divination_records` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `browser_fingerprint` text NOT NULL COMMENT '浏览器指纹',
  `session_id` varchar(36) NOT NULL COMMENT '占卜会话ID',
  `question` text NOT NULL COMMENT '占卜问题',
  `spread_id` varchar(36) DEFAULT NULL COMMENT '牌阵ID',
  `spread_name` varchar(255) DEFAULT NULL COMMENT '牌阵名称',
  `selected_cards` json DEFAULT NULL COMMENT '选中的卡牌',
  `reading_result` json DEFAULT NULL COMMENT '占卜结果',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(100) DEFAULT NULL COMMENT 'IP地址',
  INDEX `idx_fingerprint` (`browser_fingerprint`(255)),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='匿名占卜记录表';
```

**优势：**
- 数据隔离，不影响现有用户数据结构
- 便于统计和管理匿名用户行为
- 可以独立设置数据保留策略
- 扩展性好，后续可以添加更多匿名用户相关字段


### 2.2 浏览器指纹方案

#### 当前指纹实现分析
- 使用 `@fingerprintjs/fingerprintjs` 库
- 存储在用户表的 `browser_fingerprints` 字段中

#### 未登录指纹检查逻辑
1. 获取当前浏览器指纹
2. 查询 `anonymous_divination_records` 表检查是否已存在该指纹的记录
3. 如果存在记录，则该用户已使用过免费机会
4. 如果不存在记录，则可以进行免费占卜

### 2.3 权限控制逻辑重构

#### 当前权限检查流程
```
用户访问基础塔罗占卜 → 检查登录状态 → 未登录则显示登录提示 → 登录后检查VIP状态和剩余次数
```

#### 新的权限检查流程
```
用户访问基础塔罗占卜 → 检查登录状态
├─ 已登录 → 检查VIP状态和剩余次数
└─ 未登录 → 检查浏览器指纹是否已使用免费机会
   ├─ 已使用 → 显示"登录后获得更多占卜次数"提示
   └─ 未使用 → 允许进行1次免费占卜
```

## 三、具体实现方案

### 3.1 前端改造

#### 3.1.1 权限检查组件修改
需要修改以下组件的权限检查逻辑：

1. **Home.tsx** - 首页入口  
2. **AskQuestion.tsx** - 问题输入页面
3. **SpreadSelection.tsx** - 牌阵选择页面  
4. **TarotCardSelection.tsx** - 抽牌页面
5. **TarotResultPage.tsx** - 结果页面

#### 3.1.2 剩余次数显示组件
修改现有的组件来显示不同状态下的剩余次数：


### 3.2 后端改造

#### 3.2.1 新增匿名占卜API路由

```javascript
// server/routes/anonymous.js
const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../config/database');

// 检查匿名用户是否可以使用免费占卜
router.post('/check-eligibility', async (req, res) => {
  try {
    const { fingerprint } = req.body;
    
    if (!fingerprint) {
      return res.status(400).json({ error: '缺少浏览器指纹' });
    }

    const pool = await getConnection();
    const [rows] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    const hasUsed = rows[0].count > 0;
    
    res.json({
      canUse: !hasUsed,
      hasUsed: hasUsed
    });
  } catch (error) {
    console.error('检查匿名用户资格时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 记录匿名占卜
router.post('/record', async (req, res) => {
  try {
    const {
      fingerprint,
      sessionId,
      question,
      spreadId,
      spreadName,
      selectedCards,
      readingResult
    } = req.body;

    const pool = await getConnection();
    
    // 检查是否已经记录过
    const [existing] = await pool.query(
      'SELECT id FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: '该用户已使用过免费占卜机会' });
    }

    // 记录匿名占卜
    const recordId = uuidv4();
    await pool.query(
      `INSERT INTO anonymous_divination_records 
       (id, browser_fingerprint, session_id, question, spread_id, spread_name, 
        selected_cards, reading_result, ip_address) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        recordId,
        fingerprint,
        sessionId,
        question,
        spreadId,
        spreadName,
        JSON.stringify(selectedCards),
        JSON.stringify(readingResult),
        req.ip
      ]
    );

    res.json({ success: true, recordId });
  } catch (error) {
    console.error('记录匿名占卜时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
```

#### 3.2.2 修改现有API的权限检查

需要修改以下API的权限检查逻辑：

1. **server/routes/tarot.js** - 基础塔罗占卜API

将现有的 `authenticateToken` 中间件改为 `optionalAuthenticateToken`，并添加匿名用户检查逻辑。

#### 3.2.3 用户注册逻辑修改

修改用户注册时的免费次数赠送逻辑：

```javascript
// 在注册成功后检查是否有匿名占卜记录
const checkAndAdjustFreeReads = async (userId, fingerprint) => {
  const pool = await getConnection();
  
  // 检查该指纹是否有匿名占卜记录
  const [anonymousRecords] = await pool.query(
    'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
    [fingerprint]
  );
  
  const hasAnonymousRecord = anonymousRecords[0].count > 0;
  
  // 如果有匿名记录，则只赠送2次（总共3次减去已使用的1次）
  // 如果没有匿名记录，则赠送3次
  const freeReads = hasAnonymousRecord ? 2 : 3;
  
  await pool.query(
    'UPDATE users SET remaining_reads = ? WHERE id = ?',
    [freeReads, userId]
  );
  
  return freeReads;
};
```

## 四、潜在问题和解决方案

### 4.1 浏览器指纹可靠性问题

**问题：** 浏览器指纹可能因为浏览器更新、清除数据等原因发生变化

**解决方案：**
1. 使用多重验证：指纹 + IP地址 + 时间窗口
2. 设置合理的指纹匹配策略，允许一定程度的变化
3. 记录指纹的各个组成部分，进行模糊匹配

### 4.2 恶意用户绕过限制

**问题：** 用户可能通过清除浏览器数据、使用隐私模式等方式绕过限制

**解决方案：**
1. 结合IP地址限制（同一IP在短时间内的匿名占卜次数限制）
2. 添加设备特征检测
3. 实施风控策略，检测异常行为模式

### 4.3 数据一致性问题

**问题：** 用户在匿名状态下占卜后注册，如何保证数据的一致性

**解决方案：**
1. 在用户注册时检查指纹，调整免费次数
2. 可选：将匿名占卜记录关联到新注册的用户账户
3. 提供占卜历史迁移功能

### 4.4 性能影响

**问题：** 每次访问都需要检查指纹可能影响性能

**解决方案：**
1. 使用缓存机制（Redis）缓存指纹检查结果
2. 异步处理指纹检查
3. 数据库索引优化

## 五、实施计划

### 阶段一：数据库和后端API（1-2天）
1. 创建 `anonymous_divination_records` 表
2. 实现匿名占卜相关API
3. 修改现有API的权限检查逻辑

### 阶段二：前端权限控制重构（2-3天）
1. 修改各页面的权限检查逻辑
2. 实现匿名用户状态管理
3. 创建剩余次数显示组件

### 阶段三：用户注册流程优化（1天）
1. 修改注册时免费次数赠送逻辑
2. 实现指纹关联检查

### 阶段四：测试和优化（2-3天）
1. 功能测试
2. 性能测试
3. 安全测试
4. 用户体验优化

### 阶段五：上线和监控（1天）
1. 生产环境部署
2. 监控指标设置
3. 用户反馈收集

## 六、风险评估

### 高风险
- 浏览器指纹准确性可能影响用户体验
- 恶意用户可能找到绕过限制的方法

### 中风险  
- 数据库性能可能受到影响
- 现有用户的使用习惯需要适应

### 低风险
- 代码复杂度增加，维护成本上升
- 需要额外的监控和运维工作

## 七、成功指标

1. **用户转化率：** 匿名用户转为注册用户的比例
2. **用户留存率：** 使用免费占卜后的用户留存情况  
3. **占卜完成率：** 匿名用户完成占卜的比例
4. **系统性能：** API响应时间和数据库查询性能
5. **安全指标：** 恶意绕过行为的检测和防护效果

## 八、后续优化方向

1. **个性化推荐：** 基于匿名用户的占卜历史提供个性化内容
2. **社交功能：** 允许匿名用户分享占卜结果（不包含个人信息）
3. **数据分析：** 分析匿名用户行为，优化产品功能
4. **A/B测试：** 测试不同的免费次数策略对转化率的影响
