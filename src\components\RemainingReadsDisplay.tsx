import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { getBrowserFingerprint } from '../utils/fingerprint';
import { checkAnonymousEligibility } from '../services/anonymousService';

interface User {
  vipStatus: string;
  remainingReads: number;
}

interface RemainingReadsDisplayProps {
  user: User | null;
  onLoginClick?: () => void;
  className?: string;
}

const RemainingReadsDisplay: React.FC<RemainingReadsDisplayProps> = ({
  user,
  onLoginClick,
  className = ''
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const [anonymousReadsUsed, setAnonymousReadsUsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const isDark = theme === 'dark';

  // 检查匿名用户状态
  useEffect(() => {
    const checkAnonymousStatus = async () => {
      if (!user) {
        try {
          const fingerprint = await getBrowserFingerprint();
          const eligibility = await checkAnonymousEligibility(fingerprint);
          setAnonymousReadsUsed(eligibility.hasUsed);
        } catch (error) {
          console.error('检查匿名用户状态失败:', error);
          setAnonymousReadsUsed(false);
        }
      }
      setLoading(false);
    };

    checkAnonymousStatus();
  }, [user]);

  const handleLoginClick = () => {
    if (onLoginClick) {
      onLoginClick();
    } else {
      navigate('/login');
    }
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className={`h-6 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-200'}`}></div>
      </div>
    );
  }

  if (!user) {
    // 未登录状态
    if (anonymousReadsUsed) {
      return (
        <div className={`text-center p-4 rounded-lg border ${
          isDark 
            ? 'bg-gray-800/50 border-gray-700 text-gray-300' 
            : 'bg-gray-50 border-gray-200 text-gray-700'
        } ${className}`}>
          <p className="mb-3 text-sm">
            {t('anonymous.reads_exhausted', '您的免费占卜次数已用完')}
          </p>
          <button 
            onClick={handleLoginClick}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              isDark
                ? 'bg-purple-600 hover:bg-purple-700 text-white'
                : 'bg-purple-500 hover:bg-purple-600 text-white'
            }`}
          >
            {t('anonymous.login_for_more', '登录后获得更多占卜次数')}
          </button>
        </div>
      );
    } else {
      return (
        <div className={`flex items-center justify-center ${className}`}>
          <div className={`px-3 py-1 rounded-full text-sm ${
            isDark
              ? 'bg-green-900/30 text-green-400 border border-green-700'
              : 'bg-green-100 text-green-700 border border-green-200'
          }`}>
            {t('anonymous.free_reads_available', '剩余免费占卜次数：1次')}
          </div>
        </div>
      );
    }
  }
  
  // 已登录状态
  if (user.vipStatus === 'active') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`px-3 py-1 rounded-full text-sm ${
          isDark
            ? 'bg-yellow-900/30 text-yellow-400 border border-yellow-700'
            : 'bg-yellow-100 text-yellow-700 border border-yellow-200'
        }`}>
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            {t('user.vip_unlimited', 'VIP用户 - 无限次占卜')}
          </span>
        </div>
      </div>
    );
  }

  const remainingReads = Math.max(0, user.remainingReads);
  const isLowReads = remainingReads <= 1;

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className={`px-3 py-1 rounded-full text-sm ${
        isLowReads
          ? isDark
            ? 'bg-red-900/30 text-red-400 border border-red-700'
            : 'bg-red-100 text-red-700 border border-red-200'
          : isDark
            ? 'bg-blue-900/30 text-blue-400 border border-blue-700'
            : 'bg-blue-100 text-blue-700 border border-blue-200'
      }`}>
        {t('user.remaining_reads', '剩余占卜次数：{{count}}次', { count: remainingReads })}
      </div>
      {isLowReads && (
        <button
          onClick={() => navigate('/membership')}
          className={`ml-2 px-2 py-1 rounded text-xs transition-colors ${
            isDark
              ? 'bg-purple-600 hover:bg-purple-700 text-white'
              : 'bg-purple-500 hover:bg-purple-600 text-white'
          }`}
        >
          {t('user.upgrade_vip', '升级VIP')}
        </button>
      )}
    </div>
  );
};

export default RemainingReadsDisplay;
